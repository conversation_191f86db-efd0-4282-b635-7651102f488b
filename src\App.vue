<script setup lang="ts">
import { RouterView } from 'vue-router'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

// 全局错误处理
const handleGlobalError = (error: Error, errorInfo: any) => {
  console.error('App level error:', error)
  // 这里可以集成错误监控服务，如 Sentry
}

const handleErrorRetry = () => {
  // 重新加载应用
  window.location.reload()
}
</script>

<template>
  <div id="app">
    <ErrorBoundary
      fallback-title="应用加载失败"
      fallback-message="抱歉，应用遇到了一些问题。请尝试刷新页面或联系技术支持。"
      :enable-error-reporting="true"
      @error="handleGlobalError"
      @retry="handleErrorRetry"
    >
      <RouterView />
    </ErrorBoundary>
  </div>
</template>

<style>
#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
</style>
