<template>
  <a-drawer
    v-model:open="visible"
    title="配置管理"
    placement="right"
    width="400"
    :closable="true"
    @close="handleClose"
  >
    <div class="config-panel">
      <!-- 主题设置 -->
      <a-card title="主题设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="主题模式">
            <a-radio-group v-model:value="localConfig.theme" @change="handleConfigChange">
              <a-radio value="light">浅色主题</a-radio>
              <a-radio value="dark">深色主题</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 布局设置 -->
      <a-card title="布局设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="布局方向">
            <a-select v-model:value="localConfig.layoutDirection" @change="handleConfigChange">
              <a-select-option value="LR">从左到右</a-select-option>
              <a-select-option value="TB">从上到下</a-select-option>
              <a-select-option value="RL">从右到左</a-select-option>
              <a-select-option value="BT">从下到上</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldTypes" @change="handleConfigChange">
              显示字段类型
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showTableComments" @change="handleConfigChange">
              显示表注释
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldDescriptions" @change="handleConfigChange">
              显示字段描述
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showDataTypes" @change="handleConfigChange">
              显示数据类型
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 性能设置 -->
      <a-card title="性能设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="性能模式">
            <a-select v-model:value="localConfig.performanceMode" @change="handleConfigChange">
              <a-select-option value="normal">标准模式</a-select-option>
              <a-select-option value="optimized">优化模式</a-select-option>
              <a-select-option value="extreme">极速模式</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.enableVirtualRendering" @change="handleConfigChange">
              启用虚拟渲染
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 图谱设置 -->
      <a-card title="图谱设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldLevelLineage" @change="handleConfigChange">
              显示字段级血缘
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showCompleteLineage" @change="handleConfigChange">
              显示完整血缘链路
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showMiniMap" @change="handleConfigChange">
              显示缩略图
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.enableTooltips" @change="handleConfigChange">
              启用提示框
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.autoSave" @change="handleConfigChange">
              自动保存配置
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <a-space direction="vertical" style="width: 100%">
          <a-button type="primary" block @click="saveConfig" :loading="saving">
            <SaveOutlined />
            保存配置
          </a-button>

          <a-button block @click="resetConfig">
            <ReloadOutlined />
            重置为默认
          </a-button>

          <a-button block @click="exportConfig">
            <ExportOutlined />
            导出配置
          </a-button>

          <a-upload
            :show-upload-list="false"
            :before-upload="importConfig"
            accept=".json"
          >
            <a-button block>
              <ImportOutlined />
              导入配置
            </a-button>
          </a-upload>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'
import type { UserConfig } from '@/utils/configManager'
import { configManager, defaultConfig, configUtils } from '@/utils/configManager'
import { useLineageStore } from '@/stores/lineageStore'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'config-changed': [config: UserConfig]
}>()

// Store
const lineageStore = useLineageStore()

// 状态
const saving = ref(false)
const localConfig = reactive<UserConfig>({ ...defaultConfig })

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleConfigChange = () => {
  // 实时应用配置变化
  emit('config-changed', { ...localConfig })

  // 如果启用自动保存，则自动保存配置
  if (localConfig.autoSave) {
    saveConfig()
  }
}

const saveConfig = async () => {
  try {
    saving.value = true

    // 保存到配置管理器
    await configManager.updateConfig(localConfig)

    // 应用主题
    configUtils.applyTheme(localConfig.theme)

    // 更新状态管理
    lineageStore.setTheme(localConfig.theme)

    message.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const resetConfig = () => {
  Object.assign(localConfig, defaultConfig)
  handleConfigChange()
  message.info('已重置为默认配置')
}

const exportConfig = () => {
  try {
    const configJson = JSON.stringify(localConfig, null, 2)
    const blob = new Blob([configJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `lineage-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    message.success('配置导出成功')
  } catch (error) {
    console.error('导出配置失败:', error)
    message.error('配置导出失败')
  }
}

const importConfig = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const configData = JSON.parse(e.target?.result as string)

        // 验证配置格式
        if (configData && typeof configData === 'object') {
          Object.assign(localConfig, defaultConfig, configData)
          handleConfigChange()
          message.success('配置导入成功')
          resolve(true)
        } else {
          throw new Error('配置文件格式无效')
        }
      } catch (error) {
        console.error('导入配置失败:', error)
        message.error('配置文件格式无效')
        reject(error)
      }
    }

    reader.onerror = () => {
      message.error('读取配置文件失败')
      reject(new Error('读取文件失败'))
    }

    reader.readAsText(file)
  })
}

// 初始化配置
const initConfig = async () => {
  try {
    const savedConfig = await configManager.getConfig()
    Object.assign(localConfig, savedConfig)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 监听visible变化，初始化配置
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      initConfig()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.config-panel {
  padding: 0;
}

.config-section {
  margin-bottom: 16px;
}

.config-section:last-of-type {
  margin-bottom: 24px;
}

.config-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

:deep(.ant-card-head) {
  padding: 8px 16px;
  min-height: auto;
}

:deep(.ant-card-body) {
  padding: 12px 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}
</style>
