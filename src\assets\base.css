/* Modern flat design color palette */
:root {
  /* Base colors */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #fafafa;
  --vt-c-white-mute: #f5f5f5;

  --vt-c-black: #000000;
  --vt-c-black-soft: #141414;
  --vt-c-black-mute: #1f1f1f;

  /* Primary colors */
  --vt-c-primary: #1890ff;
  --vt-c-primary-light: #40a9ff;
  --vt-c-primary-dark: #096dd9;

  /* Secondary colors */
  --vt-c-secondary: #52c41a;
  --vt-c-secondary-light: #73d13d;
  --vt-c-secondary-dark: #389e0d;

  /* Neutral colors */
  --vt-c-gray-1: #f5f5f5;
  --vt-c-gray-2: #f0f0f0;
  --vt-c-gray-3: #d9d9d9;
  --vt-c-gray-4: #bfbfbf;
  --vt-c-gray-5: #8c8c8c;
  --vt-c-gray-6: #595959;
  --vt-c-gray-7: #434343;
  --vt-c-gray-8: #262626;
  --vt-c-gray-9: #1f1f1f;
  --vt-c-gray-10: #141414;

  /* Status colors */
  --vt-c-success: #52c41a;
  --vt-c-warning: #faad14;
  --vt-c-error: #ff4d4f;
  --vt-c-info: #1890ff;

  /* Divider colors */
  --vt-c-divider-light-1: rgba(0, 0, 0, 0.06);
  --vt-c-divider-light-2: rgba(0, 0, 0, 0.04);
  --vt-c-divider-dark-1: rgba(255, 255, 255, 0.12);
  --vt-c-divider-dark-2: rgba(255, 255, 255, 0.08);

  /* Text colors */
  --vt-c-text-light-1: var(--vt-c-gray-8);
  --vt-c-text-light-2: var(--vt-c-gray-6);
  --vt-c-text-light-3: var(--vt-c-gray-5);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(255, 255, 255, 0.85);
  --vt-c-text-dark-3: rgba(255, 255, 255, 0.65);
}

/* Semantic color variables for modern flat design */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);
  --color-text-secondary: var(--vt-c-text-light-2);
  --color-text-tertiary: var(--vt-c-text-light-3);

  /* Modern flat design specific variables */
  --color-primary: var(--vt-c-primary);
  --color-primary-light: var(--vt-c-primary-light);
  --color-primary-dark: var(--vt-c-primary-dark);

  --color-secondary: var(--vt-c-secondary);
  --color-secondary-light: var(--vt-c-secondary-light);
  --color-secondary-dark: var(--vt-c-secondary-dark);

  /* Surface colors */
  --color-surface: var(--vt-c-white);
  --color-surface-variant: var(--vt-c-gray-1);

  /* Shadow colors */
  --color-shadow-light: rgba(0, 0, 0, 0.04);
  --color-shadow-medium: rgba(0, 0, 0, 0.08);
  --color-shadow-heavy: rgba(0, 0, 0, 0.12);

  /* Border radius */
  --border-radius-small: 4px;
  --border-radius-medium: 6px;
  --border-radius-large: 8px;
  --border-radius-xlarge: 12px;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;

  --section-gap: 160px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black-soft);
    --color-background-soft: var(--vt-c-black-mute);
    --color-background-mute: var(--vt-c-gray-9);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-1);
    --color-text-secondary: var(--vt-c-text-dark-2);
    --color-text-tertiary: var(--vt-c-text-dark-3);

    /* Surface colors for dark theme */
    --color-surface: var(--vt-c-black-mute);
    --color-surface-variant: var(--vt-c-gray-9);

    /* Shadow colors for dark theme */
    --color-shadow-light: rgba(0, 0, 0, 0.2);
    --color-shadow-medium: rgba(0, 0, 0, 0.3);
    --color-shadow-heavy: rgba(0, 0, 0, 0.4);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.5;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 14px;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
