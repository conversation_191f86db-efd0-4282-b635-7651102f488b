<template>
  <div class="error-handling-test">
    <div class="test-header">
      <h1>错误处理机制测试</h1>
      <p>测试各种错误场景的处理效果，验证错误处理机制的完整性和有效性</p>
    </div>

    <div class="test-controls">
      <a-space wrap>
        <a-button type="primary" @click="runAllTests">
          <PlayCircleOutlined />
          运行所有测试
        </a-button>
        <a-button @click="clearResults">
          <ClearOutlined />
          清空结果
        </a-button>
        <a-button @click="exportResults">
          <DownloadOutlined />
          导出结果
        </a-button>
      </a-space>
    </div>

    <div class="test-sections">
      <!-- 错误边界测试 -->
      <a-card title="错误边界测试" class="test-section">
        <div class="test-group">
          <a-space wrap>
            <a-button @click="testComponentError">组件渲染错误</a-button>
            <a-button @click="testJavaScriptError">JavaScript运行时错误</a-button>
            <a-button @click="testPromiseRejection">Promise拒绝错误</a-button>
            <a-button @click="testAsyncError">异步操作错误</a-button>
          </a-space>
        </div>

        <!-- 错误边界演示区域 -->
        <div class="error-demo-area">
          <ErrorBoundary
            fallback-title="测试错误边界"
            fallback-message="这是一个测试错误，用于验证错误边界功能"
            @error="handleBoundaryError"
            @retry="handleBoundaryRetry"
          >
            <div v-if="showErrorComponent">
              <ErrorComponent :should-error="triggerComponentError" />
            </div>
            <div v-else>
              <a-result
                status="info"
                title="错误边界测试区域"
                sub-title="点击上方按钮触发不同类型的错误"
              />
            </div>
          </ErrorBoundary>
        </div>
      </a-card>

      <!-- 数据验证测试 -->
      <a-card title="数据验证测试" class="test-section">
        <div class="test-group">
          <a-space wrap>
            <a-button @click="testEmptyData">空数据验证</a-button>
            <a-button @click="testInvalidData">无效数据验证</a-button>
            <a-button @click="testIncompleteData">不完整数据验证</a-button>
            <a-button @click="testCorruptedData">损坏数据验证</a-button>
          </a-space>
        </div>

        <div class="validation-results">
          <a-textarea
            v-model:value="validationResults"
            placeholder="数据验证结果将显示在这里..."
            :rows="6"
            readonly
          />
        </div>
      </a-card>

      <!-- 加载状态测试 -->
      <a-card title="加载状态测试" class="test-section">
        <div class="test-group">
          <a-space wrap>
            <a-button @click="testBasicLoading">基础加载状态</a-button>
            <a-button @click="testProgressLoading">进度加载状态</a-button>
            <a-button @click="testStepLoading">步骤加载状态</a-button>
            <a-button @click="testLoadingTimeout">加载超时测试</a-button>
          </a-space>
        </div>

        <div class="loading-demo-area">
          <LoadingState
            :loading="demoLoading"
            :loading-text="demoLoadingText"
            :show-progress="demoShowProgress"
            :progress="demoProgress"
            :loading-details="demoLoadingSteps.length > 0"
            :loading-steps="demoLoadingSteps"
            :show-cancel="demoLoading"
            @cancel="handleCancelDemo"
          />
        </div>
      </a-card>

      <!-- 网络错误测试 -->
      <a-card title="网络错误测试" class="test-section">
        <div class="test-group">
          <a-space wrap>
            <a-button @click="testNetworkTimeout">网络超时</a-button>
            <a-button @click="testNetworkError">网络连接错误</a-button>
            <a-button @click="testServerError">服务器错误</a-button>
            <a-button @click="testRetryMechanism">重试机制测试</a-button>
          </a-space>
        </div>

        <div class="network-status">
          <a-descriptions title="网络测试状态" bordered size="small">
            <a-descriptions-item label="最后请求">{{ lastRequest }}</a-descriptions-item>
            <a-descriptions-item label="请求状态">{{ requestStatus }}</a-descriptions-item>
            <a-descriptions-item label="重试次数">{{ retryCount }}</a-descriptions-item>
            <a-descriptions-item label="错误信息">{{ networkError }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </a-card>

      <!-- 用户体验测试 -->
      <a-card title="用户体验测试" class="test-section">
        <div class="test-group">
          <a-space wrap>
            <a-button @click="testErrorMessages">错误消息展示</a-button>
            <a-button @click="testEmptyStates">空状态展示</a-button>
            <a-button @click="testSuccessStates">成功状态展示</a-button>
            <a-button @click="testInteractionFeedback">交互反馈测试</a-button>
          </a-space>
        </div>

        <div class="ux-demo-area">
          <LoadingState
            :error="demoError"
            :error-title="demoErrorTitle"
            :error-message="demoErrorMessage"
            :error-details="demoErrorDetails"
            :show-error-details="true"
            :show-retry="true"
            :show-report="true"

            :empty="demoEmpty"
            empty-title="演示空状态"
            empty-message="这是一个空状态演示"
            :empty-actions="demoEmptyActions"

            :success="demoSuccess"
            success-title="操作成功"
            success-message="演示操作已成功完成"
            :success-actions="demoSuccessActions"

            @retry="handleDemoRetry"
            @report="handleDemoReport"
            @empty-action="handleDemoEmptyAction"
            @success-action="handleDemoSuccessAction"
          />
        </div>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <a-card title="测试结果" class="test-results">
      <div class="results-summary">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总测试数" :value="testResults.length" />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="通过测试"
              :value="passedTests"
              :value-style="{ color: '#3f8600' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="失败测试"
              :value="failedTests"
              :value-style="{ color: '#cf1322' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功率" :value="successRate" suffix="%" />
          </a-col>
        </a-row>
      </div>

      <div class="results-list">
        <a-list
          :data-source="testResults"
          size="small"
          :pagination="{ pageSize: 10 }"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span :class="{ 'test-passed': item.passed, 'test-failed': !item.passed }">
                    {{ item.passed ? '✅' : '❌' }} {{ item.name }}
                  </span>
                </template>
                <template #description>
                  <div>
                    <div>{{ item.description }}</div>
                    <div class="test-time">执行时间: {{ item.duration }}ms</div>
                    <div v-if="item.error" class="test-error">错误: {{ item.error }}</div>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  ClearOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import LoadingState from '@/components/LoadingState.vue'
import {
  validateLineageData,
  sanitizeLineageData,
  createEmptyLineageData
} from '@/utils/graphDataTransform'
import {
  errorManager,
  ErrorType,
  handleError,
  handleNetworkError,
  handleValidationError
} from '@/utils/errorManager'

// 测试结果接口
interface TestResult {
  name: string
  description: string
  passed: boolean
  duration: number
  error?: string
  timestamp: string
}

// 状态定义
const testResults = ref<TestResult[]>([])
const validationResults = ref('')

// 错误边界测试状态
const showErrorComponent = ref(false)
const triggerComponentError = ref(false)

// 加载状态测试
const demoLoading = ref(false)
const demoLoadingText = ref('加载中...')
const demoShowProgress = ref(false)
const demoProgress = ref(0)
const demoLoadingSteps = ref<Array<{
  text: string
  status: 'pending' | 'active' | 'completed' | 'error'
}>>([])

// 网络测试状态
const lastRequest = ref('无')
const requestStatus = ref('待测试')
const retryCount = ref(0)
const networkError = ref('无')

// 用户体验测试状态
const demoError = ref(false)
const demoErrorTitle = ref('')
const demoErrorMessage = ref('')
const demoErrorDetails = ref('')
const demoEmpty = ref(false)
const demoSuccess = ref(false)

const demoEmptyActions = ref([
  { key: 'action1', text: '操作1', type: 'primary' },
  { key: 'action2', text: '操作2', type: 'default' }
])

const demoSuccessActions = ref([
  { key: 'continue', text: '继续', type: 'primary' },
  { key: 'close', text: '关闭', type: 'default' }
])

// 计算属性
const passedTests = computed(() =>
  testResults.value.filter(test => test.passed).length
)

const failedTests = computed(() =>
  testResults.value.filter(test => !test.passed).length
)

const successRate = computed(() => {
  if (testResults.value.length === 0) return 0
  return Math.round((passedTests.value / testResults.value.length) * 100)
})

// 测试工具函数
const addTestResult = (
  name: string,
  description: string,
  passed: boolean,
  duration: number,
  error?: string
) => {
  testResults.value.push({
    name,
    description,
    passed,
    duration,
    error,
    timestamp: new Date().toLocaleTimeString()
  })
}

const runTest = async (
  name: string,
  description: string,
  testFn: () => Promise<void> | void
) => {
  const startTime = Date.now()
  try {
    await testFn()
    const duration = Date.now() - startTime
    addTestResult(name, description, true, duration)
    return true
  } catch (error) {
    const duration = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : String(error)
    addTestResult(name, description, false, duration, errorMessage)
    return false
  }
}

// 错误边界测试方法
const testComponentError = async () => {
  await runTest(
    '组件渲染错误',
    '测试组件渲染时抛出错误的处理',
    async () => {
      showErrorComponent.value = true
      triggerComponentError.value = true
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  )
}

const testJavaScriptError = async () => {
  await runTest(
    'JavaScript运行时错误',
    '测试JavaScript运行时错误的捕获',
    () => {
      // 故意触发一个运行时错误
      throw new Error('这是一个测试的JavaScript运行时错误')
    }
  )
}

const testPromiseRejection = async () => {
  await runTest(
    'Promise拒绝错误',
    '测试未处理的Promise拒绝',
    async () => {
      // 创建一个被拒绝的Promise
      await Promise.reject(new Error('这是一个测试的Promise拒绝错误'))
    }
  )
}

const testAsyncError = async () => {
  await runTest(
    '异步操作错误',
    '测试异步操作中的错误处理',
    async () => {
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          reject(new Error('这是一个测试的异步操作错误'))
        }, 100)
      })
    }
  )
}

// 数据验证测试方法
const testEmptyData = async () => {
  await runTest(
    '空数据验证',
    '测试空数据的验证和处理',
    () => {
      const result = validateLineageData(null)
      validationResults.value += `空数据验证结果:\n${JSON.stringify(result, null, 2)}\n\n`

      if (!result.isValid && result.errors.length > 0) {
        return // 测试通过
      }
      throw new Error('空数据验证应该失败')
    }
  )
}

const testInvalidData = async () => {
  await runTest(
    '无效数据验证',
    '测试无效数据格式的验证',
    () => {
      const invalidData = { invalid: 'data', format: true }
      const result = validateLineageData(invalidData)
      validationResults.value += `无效数据验证结果:\n${JSON.stringify(result, null, 2)}\n\n`

      if (!result.isValid) {
        return // 测试通过
      }
      throw new Error('无效数据验证应该失败')
    }
  )
}

const testIncompleteData = async () => {
  await runTest(
    '不完整数据验证',
    '测试不完整数据的验证和修复',
    () => {
      const incompleteData = {
        tables: {
          'test_table': {
            name: 'test_table',
            fields: [
              { fieldName: 'id' }, // 缺少必需字段
              { id: 'test_table.name', tableName: 'test_table' } // 缺少fieldName
            ]
          }
        },
        nodes: [],
        edges: []
      }

      const validation = validateLineageData(incompleteData)
      const sanitized = sanitizeLineageData(incompleteData)

      validationResults.value += `不完整数据验证:\n${JSON.stringify(validation, null, 2)}\n`
      validationResults.value += `数据修复结果:\n${JSON.stringify(sanitized.report, null, 2)}\n\n`

      if (validation.warnings.length > 0 || sanitized.report.fixed.length > 0) {
        return // 测试通过
      }
      throw new Error('不完整数据应该产生警告或修复')
    }
  )
}

const testCorruptedData = async () => {
  await runTest(
    '损坏数据验证',
    '测试损坏数据的处理',
    () => {
      const corruptedData = {
        tables: null,
        nodes: 'invalid',
        edges: undefined
      }

      const result = validateLineageData(corruptedData)
      validationResults.value += `损坏数据验证结果:\n${JSON.stringify(result, null, 2)}\n\n`

      if (!result.isValid && result.errors.length > 0) {
        return // 测试通过
      }
      throw new Error('损坏数据验证应该失败')
    }
  )
}

// 加载状态测试方法
const testBasicLoading = async () => {
  await runTest(
    '基础加载状态',
    '测试基础的加载状态显示',
    async () => {
      demoLoading.value = true
      demoLoadingText.value = '正在加载数据...'
      demoShowProgress.value = false
      demoLoadingSteps.value = []

      await new Promise(resolve => setTimeout(resolve, 2000))

      demoLoading.value = false
    }
  )
}

const testProgressLoading = async () => {
  await runTest(
    '进度加载状态',
    '测试带进度条的加载状态',
    async () => {
      demoLoading.value = true
      demoLoadingText.value = '正在处理数据...'
      demoShowProgress.value = true
      demoProgress.value = 0
      demoLoadingSteps.value = []

      // 模拟进度更新
      for (let i = 0; i <= 100; i += 10) {
        demoProgress.value = i
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      demoLoading.value = false
      demoShowProgress.value = false
    }
  )
}

const testStepLoading = async () => {
  await runTest(
    '步骤加载状态',
    '测试分步骤的加载状态',
    async () => {
      demoLoading.value = true
      demoLoadingText.value = '正在执行多步骤操作...'
      demoShowProgress.value = false
      demoLoadingSteps.value = [
        { text: '初始化数据', status: 'pending' },
        { text: '验证输入', status: 'pending' },
        { text: '处理数据', status: 'pending' },
        { text: '生成结果', status: 'pending' }
      ]

      // 模拟步骤执行
      for (let i = 0; i < demoLoadingSteps.value.length; i++) {
        demoLoadingSteps.value[i].status = 'active'
        await new Promise(resolve => setTimeout(resolve, 800))
        demoLoadingSteps.value[i].status = 'completed'
      }

      demoLoading.value = false
      demoLoadingSteps.value = []
    }
  )
}

const testLoadingTimeout = async () => {
  await runTest(
    '加载超时测试',
    '测试加载操作超时的处理',
    async () => {
      demoLoading.value = true
      demoLoadingText.value = '模拟超时操作...'

      // 模拟超时
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟超时错误
      demoLoading.value = false
      throw new Error('操作超时')
    }
  )
}

// 网络错误测试方法
const testNetworkTimeout = async () => {
  await runTest(
    '网络超时测试',
    '测试网络请求超时的处理',
    async () => {
      lastRequest.value = '超时测试请求'
      requestStatus.value = '请求中...'

      try {
        // 模拟网络超时
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            reject(new Error('网络请求超时'))
          }, 1000)
        })
      } catch (error) {
        requestStatus.value = '超时失败'
        networkError.value = error instanceof Error ? error.message : '未知错误'
        throw error
      }
    }
  )
}

const testNetworkError = async () => {
  await runTest(
    '网络连接错误',
    '测试网络连接失败的处理',
    async () => {
      lastRequest.value = '连接测试请求'
      requestStatus.value = '连接中...'

      try {
        // 模拟网络连接错误
        throw new Error('网络连接失败')
      } catch (error) {
        requestStatus.value = '连接失败'
        networkError.value = error instanceof Error ? error.message : '未知错误'
        handleNetworkError(error)
        throw error
      }
    }
  )
}

const testServerError = async () => {
  await runTest(
    '服务器错误测试',
    '测试服务器返回错误的处理',
    async () => {
      lastRequest.value = '服务器测试请求'
      requestStatus.value = '处理中...'

      try {
        // 模拟服务器错误
        throw new Error('服务器内部错误 (500)')
      } catch (error) {
        requestStatus.value = '服务器错误'
        networkError.value = error instanceof Error ? error.message : '未知错误'
        throw error
      }
    }
  )
}

const testRetryMechanism = async () => {
  await runTest(
    '重试机制测试',
    '测试自动重试机制',
    async () => {
      retryCount.value = 0
      lastRequest.value = '重试测试请求'

      // 使用错误管理器的重试功能
      await errorManager.withRetry(
        async () => {
          retryCount.value++
          requestStatus.value = `重试第 ${retryCount.value} 次`

          if (retryCount.value < 3) {
            throw new Error(`重试失败 ${retryCount.value}`)
          }

          requestStatus.value = '重试成功'
          return '成功'
        },
        { maxRetries: 3, delay: 500 }
      )
    }
  )
}

// 用户体验测试方法
const testErrorMessages = async () => {
  await runTest(
    '错误消息展示',
    '测试各种错误消息的展示效果',
    async () => {
      demoError.value = true
      demoErrorTitle.value = '测试错误标题'
      demoErrorMessage.value = '这是一个测试错误消息，用于验证错误展示效果'
      demoErrorDetails.value = 'Error: 详细错误信息\n  at testFunction (test.js:123:45)\n  at Object.runTest (test.js:456:78)'

      await new Promise(resolve => setTimeout(resolve, 2000))

      demoError.value = false
    }
  )
}

const testEmptyStates = async () => {
  await runTest(
    '空状态展示',
    '测试空状态的展示效果',
    async () => {
      demoEmpty.value = true

      await new Promise(resolve => setTimeout(resolve, 2000))

      demoEmpty.value = false
    }
  )
}

const testSuccessStates = async () => {
  await runTest(
    '成功状态展示',
    '测试成功状态的展示效果',
    async () => {
      demoSuccess.value = true

      await new Promise(resolve => setTimeout(resolve, 2000))

      demoSuccess.value = false
    }
  )
}

const testInteractionFeedback = async () => {
  await runTest(
    '交互反馈测试',
    '测试用户交互的反馈效果',
    async () => {
      // 测试各种消息类型
      message.info('这是一个信息提示')
      await new Promise(resolve => setTimeout(resolve, 500))

      message.success('这是一个成功提示')
      await new Promise(resolve => setTimeout(resolve, 500))

      message.warning('这是一个警告提示')
      await new Promise(resolve => setTimeout(resolve, 500))

      message.error('这是一个错误提示')
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  )
}

// 事件处理器
const handleBoundaryError = (error: Error, errorInfo: any) => {
  console.log('错误边界捕获到错误:', error, errorInfo)
  addTestResult(
    '错误边界捕获',
    '错误边界成功捕获了组件错误',
    true,
    0
  )
}

const handleBoundaryRetry = () => {
  console.log('错误边界重试')
  showErrorComponent.value = false
  triggerComponentError.value = false
  message.info('错误边界重试成功')
}

const handleCancelDemo = () => {
  demoLoading.value = false
  message.info('演示操作已取消')
}

const handleDemoRetry = () => {
  demoError.value = false
  message.info('演示重试操作')
}

const handleDemoReport = (errorInfo: any) => {
  console.log('演示错误报告:', errorInfo)
  message.success('演示错误报告已发送')
}

const handleDemoEmptyAction = (action: any) => {
  console.log('演示空状态操作:', action)
  message.info(`执行空状态操作: ${action.text}`)
}

const handleDemoSuccessAction = (action: any) => {
  console.log('演示成功状态操作:', action)
  message.info(`执行成功状态操作: ${action.text}`)
}

// 主要测试方法
const runAllTests = async () => {
  clearResults()
  message.info('开始运行所有测试...')

  // 依次运行所有测试
  await testEmptyData()
  await testInvalidData()
  await testIncompleteData()
  await testCorruptedData()

  await testBasicLoading()
  await testProgressLoading()
  await testStepLoading()

  await testNetworkTimeout()
  await testNetworkError()
  await testServerError()
  await testRetryMechanism()

  await testErrorMessages()
  await testEmptyStates()
  await testSuccessStates()
  await testInteractionFeedback()

  message.success(`所有测试完成！通过率: ${successRate.value}%`)
}

const clearResults = () => {
  testResults.value = []
  validationResults.value = ''
  networkError.value = '无'
  requestStatus.value = '待测试'
  retryCount.value = 0
}

const exportResults = () => {
  const results = {
    summary: {
      total: testResults.value.length,
      passed: passedTests.value,
      failed: failedTests.value,
      successRate: successRate.value
    },
    tests: testResults.value,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `error-handling-test-results-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('测试结果已导出')
}
</script>

<style scoped>
.error-handling-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  margin-bottom: 8px;
  color: #1890ff;
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-section {
  margin-bottom: 24px;
}

.test-group {
  margin-bottom: 16px;
}

.error-demo-area,
.loading-demo-area,
.ux-demo-area {
  min-height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin-top: 16px;
}

.validation-results {
  margin-top: 16px;
}

.network-status {
  margin-top: 16px;
}

.test-results {
  margin-top: 32px;
}

.results-summary {
  margin-bottom: 24px;
}

.test-passed {
  color: #52c41a;
}

.test-failed {
  color: #ff4d4f;
}

.test-time {
  font-size: 12px;
  color: #999;
}

.test-error {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}
</style>
